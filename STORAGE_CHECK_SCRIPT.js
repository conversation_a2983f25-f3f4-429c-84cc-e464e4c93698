// 🔍 模板存储检查脚本
// 请在浏览器控制台中执行这个脚本来检查模板存储状态

console.log('🔍 开始检查模板存储状态...');

// 检查Chrome存储API是否可用
if (typeof chrome === 'undefined' || !chrome.storage) {
  console.error('❌ Chrome存储API不可用');
} else {
  console.log('✅ Chrome存储API可用');
  
  // 获取所有存储的数据
  chrome.storage.local.get(null).then(allData => {
    console.log('📦 所有存储数据:', allData);
    
    // 检查模板相关的存储
    const templateKeys = ['replyTemplates', 'autoReplyConfig', 'templates'];
    templateKeys.forEach(key => {
      if (allData[key]) {
        console.log(`✅ 找到存储键 "${key}":`, allData[key]);
        if (Array.isArray(allData[key])) {
          console.log(`📊 ${key} 数组长度:`, allData[key].length);
          allData[key].forEach((item, index) => {
            console.log(`📝 ${key}[${index}]:`, {
              id: item.id,
              name: item.name,
              enabled: item.enabled,
              keywords: item.keywords,
              content: item.content?.substring(0, 50) + '...'
            });
          });
        }
      } else {
        console.log(`❌ 未找到存储键 "${key}"`);
      }
    });
    
    // 检查是否有任何包含"template"的键
    const allKeys = Object.keys(allData);
    const templateRelatedKeys = allKeys.filter(key => 
      key.toLowerCase().includes('template') || 
      key.toLowerCase().includes('reply')
    );
    
    if (templateRelatedKeys.length > 0) {
      console.log('🔍 找到模板相关的存储键:', templateRelatedKeys);
    } else {
      console.log('⚠️ 没有找到任何模板相关的存储键');
    }
  }).catch(error => {
    console.error('❌ 获取存储数据失败:', error);
  });
}

// 检查页面上的扩展状态
console.log('🔍 检查页面扩展状态:', {
  hasContentScript: !!window.ctripIMContentScript,
  hasWebSocketInterceptor: !!window._ctripWebSocketInterceptorInstalled,
  hasGlobalAPI: !!window.ctripIM,
  documentReady: document.readyState,
  currentURL: window.location.href
});

// 如果有content script，尝试获取模板
if (window.ctripIMContentScript) {
  console.log('🔍 尝试从Content Script获取模板...');
  try {
    if (window.ctripIMContentScript.autoReplyEngine) {
      const templates = window.ctripIMContentScript.autoReplyEngine.getAllTemplates();
      console.log('📋 Content Script中的模板:', templates);
    } else {
      console.log('⚠️ Content Script中的自动回复引擎未初始化');
    }
  } catch (error) {
    console.error('❌ 从Content Script获取模板失败:', error);
  }
}

console.log('🔍 模板存储检查完成');
