console.log('==== popup-manager 入口文件已加载 ====');
/**
 * 弹窗管理器
 * 负责管理弹窗界面的所有逻辑
 */
import { EventEmitter } from '../utils/event-emitter.js';
import { popupLogger } from '../utils/logger.js';
import PerformanceUtils from '../utils/performance-utils.js';

export class PopupManager extends EventEmitter {
  constructor() {
    super();
    this.logger = popupLogger;
    this.currentTab = null;
    this.data = {
      connections: [],
      messageHistory: [],
      stats: {},
      status: 'disconnected'
    };
    this.refreshInterval = null;
    this.initialized = false;
    
    // 性能优化
    this.cache = new PerformanceUtils.Cache(50, 30000); // 30秒缓存
    this.performanceMonitor = new PerformanceUtils.PerformanceMonitor('PopupManager');
    
    // 节流优化的方法
    this.throttledUpdateUI = PerformanceUtils.throttle(this.updateUI.bind(this), 100);
    this.debouncedRefresh = PerformanceUtils.debounce(this.refreshData.bind(this), 500);

    // 模板事件监听器控制器
    this.templateListenerController = null;

    // 防重复调用标志
    this.isDeleting = false;
    this.isToggling = false;
  }

  /**
   * 初始化弹窗管理器
   */
  async initialize() {
    if (this.initialized) {
      this.logger.warn('弹窗管理器已经初始化');
      return;
    }

    try {
      this.logger.info('初始化弹窗管理器...');
      
      // 获取当前标签页
      await this.getCurrentTab();
      
      // 设置事件监听器
      this.setupEventListeners();
      
      // 初始化UI
      await this.initializeUI();
      
      // 加载数据
      await this.loadData();
      
      // 启动定时刷新
      this.startRefreshTimer();
      
      this.initialized = true;
      this.logger.info('弹窗管理器初始化完成');
    } catch (error) {
      this.logger.error('初始化失败:', error);
      throw error;
    }
  }

  /**
   * 获取当前标签页
   */
  async getCurrentTab() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      this.currentTab = tabs[0];
      this.logger.debug('当前标签页:', this.currentTab?.url);
    } catch (error) {
      this.logger.error('获取当前标签页失败:', error);
    }
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 确保DOM已加载后再设置UI事件监听器
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.setupUIEventListeners();
      });
    } else {
      // DOM已经加载完成，直接设置事件监听器
      this.setupUIEventListeners();
    }

    // 监听来自content script的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.source === 'ctrip-im-content') {
        this.handleContentMessage(request, sendResponse);
      }
    });
  }

  /**
   * 设置UI事件监听器
   */
  setupUIEventListeners() {
    console.log('[CtripIM Popup] 🎯 设置UI事件监听器...');

    // 标签页切换 - 优先设置，确保tab切换功能正常
    this.setupTabSwitching();

    // 刷新数据按钮
    const refreshDataBtn = document.getElementById('refreshData');
    if (refreshDataBtn) {
      refreshDataBtn.addEventListener('click', () => this.refreshData());
      console.log('[CtripIM Popup] ✅ 刷新数据按钮事件已绑定');
    }

    // 模板管理按钮
    const addTemplateBtn = document.getElementById('addTemplate');
    const importTemplatesBtn = document.getElementById('importTemplates');
    const exportTemplatesBtn = document.getElementById('exportTemplates');

    if (addTemplateBtn) {
      addTemplateBtn.addEventListener('click', () => this.showTemplateModal());
    }
    if (importTemplatesBtn) {
      importTemplatesBtn.addEventListener('click', () => this.importTemplates());
    }
    if (exportTemplatesBtn) {
      exportTemplatesBtn.addEventListener('click', () => this.exportTemplates());
    }

    // 模板分类选择
    const templateCategory = document.getElementById('templateCategory');
    if (templateCategory) {
      templateCategory.addEventListener('change', (e) => this.filterTemplates(e.target.value));
    }

    // 模态框事件
    const closeModal = document.getElementById('closeModal');
    const cancelTemplate = document.getElementById('cancelTemplate');
    const templateForm = document.getElementById('templateForm');

    if (closeModal) {
      closeModal.addEventListener('click', () => this.hideTemplateModal());
    }
    if (cancelTemplate) {
      cancelTemplate.addEventListener('click', () => this.hideTemplateModal());
    }
    if (templateForm) {
      templateForm.addEventListener('submit', (e) => this.saveTemplate(e));
    }

    // 点击模态框背景关闭
    const templateModal = document.getElementById('templateModal');
    if (templateModal) {
      templateModal.addEventListener('click', (e) => {
        if (e.target === templateModal) {
          this.hideTemplateModal();
        }
      });
    }

    // 设置页面控件
    this.setupSettingsControls();

    console.log('[CtripIM Popup] ✅ UI事件监听器设置完成');
  }

  /**
   * 设置标签页切换功能
   */
  setupTabSwitching() {
    const tabs = document.querySelectorAll('.tab-button');
    console.log('[CtripIM Popup] 🔄 找到标签页按钮数量:', tabs.length);

    tabs.forEach((tab, index) => {
      const tabName = tab.dataset.tab;
      console.log(`[CtripIM Popup] 📋 设置标签页 ${index + 1}: ${tabName}`);

      tab.addEventListener('click', (e) => {
        e.preventDefault();
        console.log(`[CtripIM Popup] 🖱️ 点击标签页: ${tabName}`);
        this.switchTab(tabName);
      });
    });

    // 设置默认激活的标签页
    this.switchTab('status');
  }

  /**
   * 设置设置页面控件
   */
  setupSettingsControls() {
    // 拦截器开关
    const enableInterceptor = document.getElementById('enableInterceptor');
    if (enableInterceptor) {
      enableInterceptor.addEventListener('change', (e) => {
        this.updateSetting('enableInterceptor', e.target.checked);
      });
    }

    // 自动回复开关
    const enableAutoReply = document.getElementById('enableAutoReply');
    if (enableAutoReply) {
      enableAutoReply.addEventListener('change', (e) => {
        this.toggleAutoReply(e.target.checked);
      });
    }

    // 日志级别
    const logLevel = document.getElementById('logLevel');
    if (logLevel) {
      logLevel.addEventListener('change', (e) => {
        this.updateSetting('logLevel', e.target.value);
      });
    }

    // 存储日志开关
    const enableStorageLog = document.getElementById('enableStorageLog');
    if (enableStorageLog) {
      enableStorageLog.addEventListener('change', (e) => {
        this.updateSetting('enableStorageLog', e.target.checked);
      });
    }
  }

  /**
   * 初始化UI
   */
  async initializeUI() {
    try {
      // 设置版本信息
      const versionElement = document.getElementById('version');
      if (versionElement) {
        versionElement.textContent = chrome.runtime.getManifest().version;
      }

      // 设置初始状态
      this.updateStatus('loading');
      
      // 显示当前URL
      const urlElement = document.getElementById('currentUrl');
      if (urlElement && this.currentTab) {
        urlElement.textContent = this.currentTab.url;
      }

      // 初始化图表
      this.initializeCharts();
      
    } catch (error) {
      this.logger.error('初始化UI失败:', error);
    }
  }

  /**
   * 加载数据
   */
  async loadData() {
    try {
      console.log('[CtripIM Popup] 🔄 开始加载数据...');
      console.log('[CtripIM Popup] 📍 当前标签页URL:', this.currentTab?.url);

      // 检查是否是携程网站
      if (!this.isCtripSite()) {
        console.log('[CtripIM Popup] ⚠️ 非携程网站，URL:', this.currentTab?.url);
        this.updateStatus('not-ctrip-site');
        this.showNotification('请在携程网站页面使用此扩展', 'warning');
        return;
      }

      // 先设置为加载状态
      this.updateStatus('loading');

      // 从content script获取数据
      console.log('[CtripIM Popup] 📤 发送get-status请求...');
      const result = await this.sendMessageToContent('get-status');

      console.log('[CtripIM Popup] 📥 获取状态结果:', result);

      if (!result?.success) {
        console.log('[CtripIM Popup] ❌ 获取状态失败:', result);
        this.handleConnectionError(result);
        return;
      }

      // 更新状态
      if (result.data) {
        const { status, activeConnections, totalConnections, messageCount, lastActivity } = result.data;

        console.log('[CtripIM Popup] 📊 状态数据:', {
          status,
          activeConnections,
          totalConnections,
          messageCount,
          lastActivity
        });

        // 更新连接状态
        this.updateStatus(status);

        // 更新统计信息
        this.updateStats({
          activeConnections,
          totalConnections,
          messageCount,
          lastActivity
        });
      } else {
        console.log('[CtripIM Popup] ⚠️ 没有返回数据，设置为未连接状态');
        this.updateStatus('disconnected');
      }

    } catch (error) {
      console.error('[CtripIM Popup] ❌ 加载数据失败:', error);
      this.handleError(error);
    }
  }

  /**
   * 处理连接错误
   */
  handleConnectionError(result) {
    console.log('[CtripIM Popup] 🚨 处理连接错误:', result);

    switch (result?.code) {
      case 'NOT_CTRIP_SITE':
        this.updateStatus('not-ctrip-site');
        this.showNotification('请在携程网站页面使用此扩展', 'warning');
        break;
      case 'NO_CURRENT_TAB':
        this.updateStatus('error');
        this.showNotification('无法获取当前标签页信息', 'error');
        break;
      case 'CONTENT_SCRIPT_NOT_LOADED':
        this.updateStatus('error');
        this.showNotification('扩展未完全加载，请刷新页面重试', 'error');
        break;
      case 'TIMEOUT':
        this.updateStatus('error');
        this.showNotification('连接超时，请检查页面状态或刷新重试', 'error');
        break;
      case 'CONNECTION_ERROR':
        this.updateStatus('error');
        this.showNotification('连接失败: ' + (result.error || '未知错误'), 'error');
        break;
      default:
        // 如果没有明确的错误代码，可能是content script正常响应但返回了失败状态
        if (result?.error) {
          this.updateStatus('disconnected'); // 使用disconnected而不是error
          this.showNotification('获取状态失败: ' + result.error, 'warning');
        } else {
          this.updateStatus('error');
          this.showNotification('未知连接错误', 'error');
        }
    }
  }





  /**
   * 加载统计信息
   */
  async loadStats() {
    try {
      const result = await this.sendMessageToContent('get-stats');
      if (result.success) {
        this.data.stats = result.data || {};
        this.updateStats();
      } else {
        this.logger.debug('加载统计信息失败:', result.error);
        this.data.stats = {};
      }
    } catch (error) {
      this.logger.error('加载统计信息失败:', error);
      this.data.stats = {};
    }
  }

  /**
   * 更新UI（新增的节流方法）
   */
  updateUI() {
    this.updateTemplatesList();
    this.updateStats();
    this.updateCharts();
  }





  /**
   * 更新统计信息
   */
  updateStats(stats) {
    const {
      activeConnections = 0,
      totalConnections = 0,
      messageCount = 0,
      lastActivity = null
    } = stats;

    // 更新活跃连接数
    const activeConnectionsElement = document.getElementById('activeConnections');
    if (activeConnectionsElement) {
      activeConnectionsElement.textContent = activeConnections;
    }

    // 更新总连接数
    const totalConnectionsElement = document.getElementById('totalConnections');
    if (totalConnectionsElement) {
      totalConnectionsElement.textContent = totalConnections;
    }

    // 更新消息数量
    const messageCountElement = document.getElementById('messageCount');
    if (messageCountElement) {
      messageCountElement.textContent = messageCount;
    }

    // 更新最后活动时间
    const lastActivityElement = document.getElementById('lastActivity');
    if (lastActivityElement && lastActivity) {
      const timeAgo = Math.floor((Date.now() - lastActivity) / 1000);
      lastActivityElement.textContent = `${timeAgo}秒前`;
    }
  }

  /**
   * 更新统计元素
   */
  updateStatElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
      element.textContent = value;
    }
  }

  /**
   * 更新状态
   */
  updateStatus(status) {
    console.log('[CtripIM Popup] 📊 状态更新:', {
      newStatus: status,
      currentStatus: this.data.status,
      timestamp: Date.now()
    });

    // 更新内部状态
    this.data.status = status;

    // 更新UI元素
    const statusElement = document.getElementById('extensionStatus');
    const statusIndicator = document.getElementById('statusIndicator');
    const statusText = document.querySelector('.status-text');

    if (statusElement) {
      let statusClass = '';
      let statusMessage = '';

      switch (status) {
        case 'connected':
          statusClass = 'connected';
          statusMessage = '已连接';
          break;
        case 'disconnected':
          statusClass = 'disconnected';
          statusMessage = '未连接';
          break;
        case 'loading':
          statusClass = 'loading';
          statusMessage = '检测中...';
          break;
        case 'error':
          statusClass = 'error';
          statusMessage = '连接错误';
          break;
        case 'not-ctrip-site':
          statusClass = 'not-ctrip-site';
          statusMessage = '非携程网站';
          break;
        default:
          statusClass = 'disconnected';
          statusMessage = '未连接';
      }

      // 更新状态显示
      statusElement.textContent = statusMessage;
      statusElement.className = `value ${statusClass}`;
      
      // 更新状态指示器
      if (statusIndicator) {
        statusIndicator.className = `status-indicator ${statusClass}`;
      }
      
      // 更新状态文本
      if (statusText) {
        statusText.textContent = statusMessage;
      }
    }
  }

  /**
   * 处理错误
   */
  handleError(error) {
    console.error('[CtripIM Popup] ❌ 错误:', error);
    this.updateStatus('error');
    this.showNotification(error.message || '发生错误', 'error');
  }

  /**
   * 切换标签页
   */
  switchTab(tabName) {
    console.log(`[CtripIM Popup] 🔄 切换到标签页: ${tabName}`);

    // 更新标签按钮状态
    const tabs = document.querySelectorAll('.tab-button');
    console.log(`[CtripIM Popup] 📋 找到标签按钮数量: ${tabs.length}`);

    tabs.forEach(tab => {
      const isActive = tab.dataset.tab === tabName;
      if (isActive) {
        tab.classList.add('active');
        console.log(`[CtripIM Popup] ✅ 激活标签按钮: ${tabName}`);
      } else {
        tab.classList.remove('active');
      }
    });

    // 更新内容显示
    const contents = document.querySelectorAll('.tab-content');
    console.log(`[CtripIM Popup] 📄 找到内容面板数量: ${contents.length}`);

    contents.forEach(content => {
      const expectedId = `${tabName}Tab`;
      const isActive = content.id === expectedId;

      if (isActive) {
        content.classList.add('active');
        console.log(`[CtripIM Popup] ✅ 显示内容面板: ${expectedId}`);
      } else {
        content.classList.remove('active');
      }
    });

    // 根据标签页加载相应数据
    this.loadTabData(tabName);
  }

  /**
   * 保存模板
   */
  async saveTemplate(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);

    const template = {
      name: formData.get('templateName') || document.getElementById('templateName').value,
      category: formData.get('templateCategoryEdit') || document.getElementById('templateCategoryEdit').value,
      content: formData.get('templateContent') || document.getElementById('templateContent').value,
      keywords: (formData.get('templateKeywords') || document.getElementById('templateKeywords').value)
        .split(',').map(k => k.trim()).filter(k => k),
      enabled: document.getElementById('templateEnabled').checked
    };

    // 验证必填字段
    if (!template.name || !template.content) {
      this.showNotification('请填写模板名称和内容', 'error');
      return;
    }

    try {
      const isEdit = form.dataset.editId;
      const action = isEdit ? 'update-template' : 'add-template';
      const data = isEdit ? { ...template, id: form.dataset.editId } : template;

      const result = await this.sendMessageToContent(action, data);

      if (result.success) {
        this.hideTemplateModal();
        await this.loadTemplates();
        this.showNotification(isEdit ? '模板更新成功' : '模板添加成功');
      } else {
        this.showNotification('保存失败: ' + result.error, 'error');
      }
    } catch (error) {
      this.logger.error('保存模板失败:', error);
      this.showNotification('保存失败', 'error');
    }
  }

  /**
   * 编辑模板
   */
  editTemplate(templateId) {
    this.showTemplateModal(templateId);
  }

  /**
   * 删除模板
   */
  async deleteTemplate(templateId) {
    // 防重复调用
    if (this.isDeleting) {
      return;
    }

    if (!confirm('确定要删除这个模板吗？')) {
      return;
    }

    try {
      this.isDeleting = true;
      const result = await this.sendMessageToContent('delete-template', { id: templateId });

      if (result.success) {
        await this.loadTemplates();
        this.showNotification('模板删除成功');
      } else {
        this.showNotification('删除失败: ' + result.error, 'error');
      }
    } catch (error) {
      this.logger.error('删除模板失败:', error);
      this.showNotification('删除失败', 'error');
    } finally {
      this.isDeleting = false;
    }
  }

  /**
   * 切换模板启用状态
   */
  async toggleTemplate(templateId) {
    // 防重复调用
    if (this.isToggling) {
      return;
    }

    try {
      this.isToggling = true;
      const template = this.data.templates.find(t => t.id === templateId);
      if (!template) return;

      const result = await this.sendMessageToContent('toggle-template', {
        id: templateId,
        enabled: !template.enabled
      });

      if (result.success) {
        await this.loadTemplates();
        this.showNotification(template.enabled ? '模板已禁用' : '模板已启用');
      } else {
        this.showNotification('操作失败: ' + result.error, 'error');
      }
    } catch (error) {
      this.logger.error('切换模板状态失败:', error);
      this.showNotification('操作失败', 'error');
    } finally {
      this.isToggling = false;
    }
  }

  /**
   * 过滤模板
   */
  filterTemplates(category) {
    this.updateTemplatesList(category);
  }

  /**
   * 根据标签页加载数据
   */
  async loadTabData(tabName) {
    try {
      switch (tabName) {
        case 'status':
          // 状态页面已在初始化时加载
          break;
        case 'messages':
          await this.loadTemplates();
          break;
        case 'settings':
          await this.loadSettings();
          break;
      }
    } catch (error) {
      console.error(`[CtripIM Popup] ❌ 加载${tabName}数据失败:`, error);
    }
  }



  /**
   * 导入模板
   */
  async importTemplates() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = async (event) => {
      const file = event.target.files[0];
      if (!file) return;

      try {
        const text = await file.text();
        const templates = JSON.parse(text);

        if (!Array.isArray(templates)) {
          this.showNotification('无效的模板文件格式', 'error');
          return;
        }

        const result = await this.sendMessageToContent('import-templates', { templates });

        if (result.success) {
          await this.loadTemplates();
          this.showNotification(`成功导入 ${result.data.count} 个模板`);
        } else {
          this.showNotification('导入失败: ' + result.error, 'error');
        }
      } catch (error) {
        this.logger.error('导入模板失败:', error);
        this.showNotification('导入失败: 文件格式错误', 'error');
      }
    };

    input.click();
  }

  /**
   * 导出模板
   */
  async exportTemplates() {
    try {
      const result = await this.sendMessageToContent('export-templates');

      if (result.success) {
        const dataStr = JSON.stringify(result.data, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `ctrip-im-templates-${new Date().toISOString().split('T')[0]}.json`;
        a.click();

        URL.revokeObjectURL(url);
        this.showNotification('模板导出成功');
      } else {
        this.showNotification('导出失败: ' + result.error, 'error');
      }
    } catch (error) {
      this.logger.error('导出模板失败:', error);
      this.showNotification('导出失败', 'error');
    }
  }

  /**
   * 切换自动回复状态
   */
  async toggleAutoReply(enabled) {
    try {
      const result = await this.sendMessageToContent('toggle-auto-reply', { enabled });

      if (result.success) {
        await this.updateAutoReplyStatus();
        this.showNotification(result.data.enabled ? '自动回复已启用' : '自动回复已禁用');
      } else {
        this.showNotification('操作失败: ' + result.error, 'error');
        // 恢复复选框状态
        const checkbox = document.getElementById('enableAutoReply');
        if (checkbox) {
          checkbox.checked = !enabled;
        }
      }
    } catch (error) {
      this.logger.error('切换自动回复失败:', error);
      this.showNotification('操作失败', 'error');
      // 恢复复选框状态
      const checkbox = document.getElementById('enableAutoReply');
      if (checkbox) {
        checkbox.checked = !enabled;
      }
    }
  }

  /**
   * 更新自动回复状态显示
   */
  async updateAutoReplyStatus() {
    try {
      const result = await this.sendMessageToContent('get-auto-reply-status');

      if (result.success) {
        const { enabled, activeTemplates, todayReplies } = result.data;

        // 更新复选框状态
        const checkbox = document.getElementById('enableAutoReply');
        if (checkbox) {
          checkbox.checked = enabled;
        }

        // 更新状态显示
        const statusCard = document.getElementById('autoReplyStatusCard');
        const activeTemplatesElement = document.getElementById('activeTemplatesCount');
        const todayRepliesElement = document.getElementById('todayRepliesCount');

        if (statusCard) {
          statusCard.style.display = enabled ? 'block' : 'none';
        }

        if (activeTemplatesElement) {
          activeTemplatesElement.textContent = activeTemplates || 0;
        }

        if (todayRepliesElement) {
          todayRepliesElement.textContent = todayReplies || 0;
        }
      }
    } catch (error) {
      this.logger.error('更新自动回复状态失败:', error);
    }
  }

  /**
   * 获取分类名称
   */
  getCategoryName(category) {
    const categoryNames = {
      greeting: '问候语',
      inquiry: '咨询回复',
      complaint: '投诉处理',
      general: '通用回复',
      custom: '自定义'
    };
    return categoryNames[category] || category;
  }

  /**
   * 刷新数据
   */
  async refreshData() {
    try {
      const refreshBtn = document.getElementById('refreshBtn');
      if (refreshBtn) {
        refreshBtn.disabled = true;
        refreshBtn.textContent = '刷新中...';
      }

      await this.loadData();

      if (refreshBtn) {
        refreshBtn.disabled = false;
        refreshBtn.textContent = '刷新';
      }
    } catch (error) {
      this.logger.error('刷新数据失败:', error);
    }
  }

  /**
   * 加载模板列表
   */
  async loadTemplates() {
    try {
      const result = await this.sendMessageToContent('get-templates');
      if (result.success) {
        this.data.templates = result.data || [];
        this.updateTemplatesList();
      } else {
        this.logger.debug('加载模板失败:', result.error);
        this.data.templates = [];
      }
    } catch (error) {
      this.logger.error('加载模板失败:', error);
      this.data.templates = [];
    }
  }

  /**
   * 更新模板列表显示
   */
  updateTemplatesList(category = '') {
    const templatesList = document.getElementById('templatesList');
    if (!templatesList) return;

    const templates = this.data.templates || [];
    const filteredTemplates = !category || category === ''
      ? templates
      : templates.filter(t => t.category === category);

    if (filteredTemplates.length === 0) {
      templatesList.innerHTML = '<div class="empty-state">暂无模板，点击"添加模板"开始创建</div>';
      return;
    }

    templatesList.innerHTML = filteredTemplates.map(template => `
      <div class="template-item ${template.enabled ? '' : 'disabled'}" data-id="${template.id}">
        <div class="template-content">
          <div class="template-text">${this.escapeHtml(template.content)}</div>
          <div class="template-actions">
            <button class="template-action-btn edit" data-template-id="${template.id}" data-action="edit" title="编辑模板">
              ✏️ 编辑
            </button>
            <button class="template-action-btn toggle ${template.enabled ? '' : 'disabled'}"
                    data-template-id="${template.id}" data-action="toggle" data-enabled="${template.enabled}"
                    title="${template.enabled ? '点击禁用' : '点击启用'}">
              ${template.enabled ? '✅ 启用' : '❌ 禁用'}
            </button>
            <button class="template-action-btn delete" data-template-id="${template.id}" data-action="delete" title="删除模板">
              🗑️ 删除
            </button>
          </div>
        </div>
      </div>
    `).join('');

    // 添加事件监听器
    this.attachTemplateActionListeners();
  }

  /**
   * 为模板操作按钮添加事件监听器
   */
  attachTemplateActionListeners() {
    const templatesList = document.getElementById('templatesList');
    if (!templatesList) return;

    // 如果已有监听器，先移除
    if (this.templateListenerController) {
      this.templateListenerController.abort();
    }

    // 创建新的AbortController
    this.templateListenerController = new AbortController();

    // 使用事件委托，监听父容器的点击事件
    templatesList.addEventListener('click', (e) => {
      const button = e.target.closest('.template-action-btn');
      if (!button) return;

      const templateId = button.dataset.templateId;
      const action = button.dataset.action;

      if (!templateId || !action) return;

      switch (action) {
        case 'edit':
          this.editTemplate(templateId);
          break;
        case 'toggle':
          this.toggleTemplate(templateId);
          break;
        case 'delete':
          this.deleteTemplate(templateId);
          break;
        default:
          console.warn('未知的模板操作:', action);
      }
    }, { signal: this.templateListenerController.signal });
  }

  /**
   * 显示模板编辑弹窗
   */
  showTemplateModal(templateId = null) {
    const modal = document.getElementById('templateModal');
    const modalTitle = document.getElementById('modalTitle');
    const form = document.getElementById('templateForm');

    if (!modal || !modalTitle || !form) return;

    // 重置表单
    form.reset();

    if (templateId) {
      // 编辑模式
      const template = this.data.templates.find(t => t.id === templateId);
      if (template) {
        modalTitle.textContent = '编辑消息模板';
        document.getElementById('templateName').value = template.name;
        document.getElementById('templateCategoryEdit').value = template.category;
        document.getElementById('templateContent').value = template.content;
        document.getElementById('templateKeywords').value = template.keywords ? template.keywords.join(', ') : '';
        document.getElementById('templateEnabled').checked = template.enabled;
        form.dataset.editId = templateId;
      }
    } else {
      // 新增模式
      modalTitle.textContent = '添加消息模板';
      document.getElementById('templateEnabled').checked = true;
      delete form.dataset.editId;
    }

    modal.style.display = 'flex';
  }

  /**
   * 隐藏模板编辑弹窗
   */
  hideTemplateModal() {
    const modal = document.getElementById('templateModal');
    if (modal) {
      modal.style.display = 'none';
    }
  }

  /**
   * 发送消息
   */
  async sendMessage() {
    try {
      const messageInput = document.getElementById('messageInput');
      const connectionSelect = document.getElementById('connectionSelect');
      
      if (!messageInput || !connectionSelect) return;
      
      const message = messageInput.value.trim();
      const connectionId = connectionSelect.value;
      
      if (!message || !connectionId) {
        this.showNotification('请输入消息内容并选择连接');
        return;
      }
      
      const result = await this.sendMessageToContent('send-message', {
        connectionId,
        message
      });
      
      if (result.success) {
        messageInput.value = '';
        this.showNotification('消息发送成功');
        await this.loadMessageHistory();
      } else {
        this.showNotification('消息发送失败: ' + result.message);
      }
    } catch (error) {
      this.logger.error('发送消息失败:', error);
    }
  }

  /**
   * 发送消息到content script
   */
  async sendMessageToContent(action, data = {}, retries = 3) {
    console.log(`[CtripIM Popup] 📤 发送消息到content script:`, { action, data, retries });
    console.log(`[CtripIM Popup] 📍 当前标签页:`, {
      id: this.currentTab?.id,
      url: this.currentTab?.url,
      title: this.currentTab?.title
    });

    // 检查当前标签页
    if (!this.currentTab || !this.currentTab.id) {
      console.log('[CtripIM Popup] ❌ 没有当前标签页信息');
      return {
        success: false,
        error: '没有当前标签页信息',
        code: 'NO_CURRENT_TAB'
      };
    }

    // 检查是否是携程网站
    const isCtripSite = this.isCtripSite();
    console.log(`[CtripIM Popup] 🔍 网站检测结果:`, isCtripSite);

    if (!isCtripSite) {
      console.log('[CtripIM Popup] ⚠️ 非携程网站，但仍尝试通信');
      // 对于非携程网站，先尝试注入content script
      await this.tryInjectContentScript();
    }

    // 增加超时和重试机制
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        console.log(`[CtripIM Popup] 🔄 第${attempt}次尝试发送消息...`);
        const response = await this.sendMessageWithTimeout(action, data, 10000); // 增加超时时间到10秒
        console.log(`[CtripIM Popup] ✅ 第${attempt}次尝试成功:`, response);
        return response;
      } catch (error) {
        console.log(`[CtripIM Popup] ❌ 第${attempt}次尝试失败:`, error.message);

        if (attempt === retries) {
          // 最后一次尝试失败，返回详细错误信息
          if (error.message.includes('Receiving end does not exist')) {
            return {
              success: false,
              error: `Content script未加载。当前页面: ${this.currentTab?.url}。请确保在携程网站页面使用此扩展，或刷新页面重试。`,
              code: 'CONTENT_SCRIPT_NOT_LOADED'
            };
          } else if (error.message.includes('消息发送超时')) {
            return {
              success: false,
              error: 'Content script响应超时，请检查页面状态或刷新重试',
              code: 'TIMEOUT'
            };
          } else {
            return {
              success: false,
              error: `通信错误: ${error.message}`,
              code: 'CONNECTION_ERROR'
            };
          }
        }

        // 等待一段时间后重试
        const delay = 1000 * attempt;
        console.log(`[CtripIM Popup] ⏳ 等待${delay}ms后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * 尝试注入content script（用于非携程网站的测试）
   */
  async tryInjectContentScript() {
    try {
      console.log('[CtripIM Popup] 🔧 尝试注入content script...');

      // 使用chrome.scripting API注入content script
      await chrome.scripting.executeScript({
        target: { tabId: this.currentTab.id },
        files: ['content/content-script-bundle.js']
      });

      console.log('[CtripIM Popup] ✅ Content script注入成功');

      // 等待一段时间让content script初始化
      await new Promise(resolve => setTimeout(resolve, 2000));

    } catch (error) {
      console.log('[CtripIM Popup] ❌ Content script注入失败:', error.message);
    }
  }

  /**
   * 带超时的消息发送
   */
  sendMessageWithTimeout(action, data, timeout = 10000) {
    return new Promise((resolve, reject) => {
      let isResolved = false;

      // 设置超时
      const timeoutId = setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          console.error(`[CtripIM Popup] ⏰ 消息超时: ${action} (${timeout}ms)`);
          reject(new Error(`消息发送超时 (${timeout}ms): ${action}`));
        }
      }, timeout);

      try {
        chrome.tabs.sendMessage(
          this.currentTab.id,
          {
            source: 'ctrip-im-popup',
            action,
            data,
            timestamp: Date.now()
          },
          (response) => {
            if (!isResolved) {
              isResolved = true;
              clearTimeout(timeoutId);

              if (chrome.runtime.lastError) {
                console.error('[CtripIM Popup] ❌ Chrome runtime error:', chrome.runtime.lastError);
                reject(new Error(chrome.runtime.lastError.message));
              } else if (!response) {
                console.warn('[CtripIM Popup] ⚠️ 收到空响应，可能content script未正确处理消息');
                reject(new Error('Content script返回空响应，请检查页面是否支持此扩展'));
              } else if (response.success === false) {
                console.error('[CtripIM Popup] ❌ Content script返回错误:', response.error);
                reject(new Error(response.error || '未知错误'));
              } else {
                console.log('[CtripIM Popup] ✅ 收到有效响应:', response);
                resolve(response);
              }
            }
          }
        );
      } catch (error) {
        if (!isResolved) {
          isResolved = true;
          clearTimeout(timeoutId);
          console.error('[CtripIM Popup] ❌ 发送消息异常:', error);
          reject(error);
        }
      }
    });
  }

  /**
   * 处理content script消息
   */
  handleContentMessage(request, sendResponse) {
    const { action, data } = request;
    
    console.log('[CtripIM Popup] 📨 收到content script消息:', {
      action,
      data,
      timestamp: Date.now()
    });
    
    switch (action) {
      case 'connection-status-changed':
        this.updateStatus(data.status === 'connected' ? 'connected' : 'disconnected');
        break;
      case 'status-update':
        this.updateStatus(data.status);
        break;
      case 'template-updated':
        this.loadTemplates();
        break;
      case 'stats-updated':
        this.data.stats = data;
        this.updateStats();
        break;
    }
    
    if (sendResponse) {
      sendResponse({ success: true });
    }
  }

  /**
   * 检查是否是携程网站
   */
  isCtripSite() {
    if (!this.currentTab || !this.currentTab.url) {
      console.log('[CtripIM Popup] ⚠️ 没有当前标签页信息');
      return false;
    }

    const url = this.currentTab.url;
    console.log('[CtripIM Popup] 🔍 检查网站URL:', url);

    // 更宽松的检测逻辑，支持多种携程域名
    const ctripPatterns = [
      /^https?:\/\/.*\.ctrip\.com/,
      /^https?:\/\/.*\.trip\.com/,
      /^https?:\/\/ctrip\.com/,
      /^https?:\/\/trip\.com/,
      /^https?:\/\/.*\.ctripbiz\.com/,
      /^https?:\/\/.*\.tripbiz\.com/
    ];

    const isCtripSite = ctripPatterns.some(pattern => pattern.test(url));
    console.log('[CtripIM Popup] 🎯 网站检测结果:', isCtripSite);

    return isCtripSite;
  }

  /**
   * 获取状态颜色
   */
  getStatusColor(status) {
    const colors = {
      connected: '#4CAF50',
      connecting: '#FF9800',
      disconnected: '#F44336',
      error: '#F44336',
      closed: '#757575'
    };
    return colors[status] || '#757575';
  }

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    if (!timestamp) return '从未';
    return new Date(timestamp).toLocaleString();
  }

  /**
   * 转义HTML
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 显示通知
   */
  showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 自动移除
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  /**
   * 初始化图表
   */
  initializeCharts() {
    // 这里可以添加图表初始化代码
    // 例如使用Chart.js或其他图表库
  }

  /**
   * 更新图表
   */
  updateCharts() {
    // 这里可以添加图表更新代码
  }

  /**
   * 启动定时刷新
   */
  startRefreshTimer() {
    // 每30秒刷新一次数据，使用防抖优化
    this.refreshInterval = setInterval(() => {
      this.debouncedRefresh();
    }, 30000);
  }

  /**
   * 停止定时刷新
   */
  stopRefreshTimer() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }
  }

  /**
   * 打开设置页面
   */
  openSettings() {
    chrome.runtime.openOptionsPage();
  }





  /**
   * 加载设置
   */
  async loadSettings() {
    try {
      const result = await this.sendMessageToContent('get-settings');
      if (result.success) {
        this.updateSettingsUI(result.data || {});
      }

      // 加载自动回复状态
      await this.updateAutoReplyStatus();
    } catch (error) {
      console.error('[CtripIM Popup] ❌ 加载设置失败:', error);
    }
  }

  /**
   * 更新设置UI
   */
  updateSettingsUI(settings) {
    const {
      enableInterceptor = true,
      enableAutoReply = false,
      logLevel = 'info',
      enableStorageLog = true
    } = settings;

    // 更新控件状态
    const enableInterceptorEl = document.getElementById('enableInterceptor');
    if (enableInterceptorEl) enableInterceptorEl.checked = enableInterceptor;

    const enableAutoReplyEl = document.getElementById('enableAutoReply');
    if (enableAutoReplyEl) enableAutoReplyEl.checked = enableAutoReply;

    const logLevelEl = document.getElementById('logLevel');
    if (logLevelEl) logLevelEl.value = logLevel;

    const enableStorageLogEl = document.getElementById('enableStorageLog');
    if (enableStorageLogEl) enableStorageLogEl.checked = enableStorageLog;
  }

  /**
   * 更新设置
   */
  async updateSetting(key, value) {
    try {
      const result = await this.sendMessageToContent('update-setting', { key, value });
      if (result.success) {
        console.log(`[CtripIM Popup] ✅ 设置已更新: ${key} = ${value}`);
      }
    } catch (error) {
      console.error('[CtripIM Popup] ❌ 更新设置失败:', error);
    }
  }



  /**
   * 下载文件
   */
  downloadFile(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();

    URL.revokeObjectURL(url);
  }

  /**
   * 销毁弹窗管理器
   */
  destroy() {
    this.logger.info('销毁弹窗管理器');

    this.stopRefreshTimer();
    this.removeAllListeners();

    this.initialized = false;
  }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', async () => {
  const popupManager = new PopupManager();
  await popupManager.initialize();
  
  // 导出全局对象供调试使用
  window.popupManager = popupManager;
});

export default PopupManager; 