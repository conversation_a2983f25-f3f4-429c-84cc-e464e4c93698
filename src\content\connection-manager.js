/**
 * 连接管理器
 * 负责管理WebSocket连接的生命周期和状态
 */
import { EventEmitter } from '../utils/event-emitter.js';
import { contentLogger } from '../utils/logger.js';
import PerformanceUtils from '../utils/performance-utils.js';

export class ConnectionManager extends EventEmitter {
  constructor() {
    super();
    this.logger = contentLogger;
    this.connections = new Map();
    this.messageHistory = [];
    this.cleanupInterval = null;
    this.maxHistorySize = 1000;
    this.initialized = false;
    
    // 性能优化
    this.cache = new PerformanceUtils.Cache(20, 60000); // 1分钟缓存
    this.batchProcessor = new PerformanceUtils.BatchProcessor(
      this.processBatchMessages.bind(this),
      5, // 批处理大小
      200 // 延迟毫秒
    );
    
    // 节流优化的方法
    this.throttledUpdateStats = PerformanceUtils.throttle(this.updateStatsCache.bind(this), 1000);
    this.debouncedCleanup = PerformanceUtils.debounce(this.cleanupExpiredConnections.bind(this), 30000);
  }

  /**
   * 初始化连接管理器
   */
  async initialize() {
    if (this.initialized) {
      this.logger.warn('连接管理器已经初始化');
      return;
    }

    try {
      this.logger.info('初始化连接管理器...');
      
      // 启动清理定时器
      this.startCleanupTimer();
      
      // 设置消息监听器
      this.setupMessageListener();
      
      this.initialized = true;
      this.logger.info('连接管理器初始化完成');
    } catch (error) {
      this.logger.error('初始化失败:', error);
      throw error;
    }
  }

  /**
   * 注册新连接
   */
  registerConnection(connectionInfo) {
    const { id, url, protocols } = connectionInfo;
    
    if (this.connections.has(id)) {
      this.logger.warn('连接已存在:', id);
      return;
    }

    const connection = {
      id,
      url,
      protocols,
      status: 'connecting',
      createdAt: Date.now(),
      lastActivity: Date.now(),
      messageCount: 0,
      receivedCount: 0,
      sentCount: 0,
      errorCount: 0,
      ...connectionInfo
    };

    this.connections.set(id, connection);
    this.logger.info('连接已注册:', { id, url });
    
    this.emit('connection-registered', connection);
    return connection;
  }

  /**
   * 更新连接状态
   */
  updateConnectionStatus(connectionId, status, additionalData = {}) {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      this.logger.warn('连接不存在，尝试注册新连接:', connectionId);
      // 如果连接不存在，尝试注册新连接
      const newConnection = this.registerConnection({
        id: connectionId,
        status: status,
        createdAt: Date.now(),
        lastActivity: Date.now(),
        ...additionalData
      });
      
      if (newConnection) {
        this.logger.info('已自动注册新连接:', connectionId);
        // 广播新连接状态
        this.broadcastStatusUpdate(connectionId, status, newConnection);
      }
      return;
    }

    const oldStatus = connection.status;
    connection.status = status;
    connection.lastActivity = Date.now();
    
    // 合并其他数据
    Object.assign(connection, additionalData);

    this.logger.info('连接状态更新:', { 
      connectionId, 
      oldStatus, 
      newStatus: status,
      additionalData 
    });
    
    // 广播状态更新
    this.broadcastStatusUpdate(connectionId, status, connection);

    // 如果连接关闭，启动清理
    if (status === 'closed' || status === 'error') {
      this.scheduleConnectionCleanup(connectionId);
    }
  }

  /**
   * 广播状态更新
   */
  broadcastStatusUpdate(connectionId, status, connection) {
    const statusData = {
      connectionId,
      status,
      connection: { ...connection },
      timestamp: Date.now()
    };

    // 触发内部事件
    this.emit('connection-status-changed', statusData);

    // 通知content script
    try {
      window.dispatchEvent(new CustomEvent('connectionStatusChanged', {
        detail: statusData
      }));
    } catch (error) {
      this.logger.error('触发状态变化事件失败:', error);
    }

    // 通知popup和background
    try {
      chrome.runtime.sendMessage({
        source: 'connection-manager',
        action: 'connection-status-changed',
        data: statusData
      });
    } catch (error) {
      this.logger.error('通知扩展组件失败:', error);
    }

    // 更新统计信息
    this.updateStats();
  }

  /**
   * 更新统计信息
   */
  updateStats() {
    const activeConns = Array.from(this.connections.values()).filter(
      conn => conn.status === 'connected'
    );

    const stats = {
      totalConnections: this.connections.size,
      activeConnections: activeConns.length,
      totalMessages: Array.from(this.connections.values()).reduce(
        (sum, conn) => sum + (conn.messageCount || 0), 0
      ),
      lastActivity: Math.max(
        ...Array.from(this.connections.values())
          .map(conn => conn.lastActivity || 0)
      )
    };

    // 通知popup
    try {
      chrome.runtime.sendMessage({
        source: 'connection-manager',
        action: 'stats-updated',
        data: stats
      });
    } catch (error) {
      this.logger.error('通知统计更新失败:', error);
    }

    return stats;
  }

  /**
   * 批处理消息记录
   */
  async processBatchMessages(messages) {
    try {
      for (const messageData of messages) {
        this.messageHistory.push(messageData);
      }

      // 限制历史记录大小
      if (this.messageHistory.length > this.maxHistorySize) {
        const removeCount = this.messageHistory.length - this.maxHistorySize;
        this.messageHistory.splice(0, removeCount);
      }

      // 触发批量更新事件
      this.emit('messages-batch-recorded', messages);
    } catch (error) {
      this.logger.error('批处理消息失败:', error);
    }
  }

  /**
   * 记录消息（优化版）
   */
  recordMessage(connectionId, message, direction) {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      this.logger.warn('连接不存在:', connectionId);
      return null;
    }

    const messageData = {
      id: this.generateMessageId(),
      connectionId,
      message,
      direction,
      timestamp: Date.now(),
      size: new Blob([JSON.stringify(message)]).size
    };

    // 使用批处理器处理消息
    this.batchProcessor.add(messageData);
    
    // 更新连接活动时间
    connection.lastActivity = Date.now();
    connection.messageCount++;
    
    // 节流更新统计
    this.throttledUpdateStats();
    
    this.emit('message-recorded', messageData);
    return messageData;
  }

  /**
   * 获取连接
   */
  getConnection(connectionId) {
    return this.connections.get(connectionId);
  }

  /**
   * 获取所有连接
   */
  getAllConnections() {
    return Array.from(this.connections.values());
  }

  /**
   * 获取活跃连接
   */
  getActiveConnections() {
    return Array.from(this.connections.values()).filter(
      conn => conn.status === 'connected' || conn.status === 'connecting'
    );
  }

  /**
   * 获取消息历史
   */
  getMessageHistory(connectionId = null, limit = 100) {
    let messages = this.messageHistory;
    
    if (connectionId) {
      messages = messages.filter(msg => msg.connectionId === connectionId);
    }
    
    return messages.slice(-limit);
  }

  /**
   * 获取统计信息（带缓存优化）
   */
  getStats() {
    // 检查缓存
    const cachedStats = this.cache.get('connection-stats');
    if (cachedStats) {
      return cachedStats;
    }

    // 计算统计信息
    const stats = {
      totalConnections: this.connections.size,
      activeConnections: Array.from(this.connections.values())
        .filter(conn => conn.status === 'connected').length,
      totalMessages: this.messageHistory.length,
      messagesSent: this.messageHistory.filter(msg => msg.direction === 'sent').length,
      messagesReceived: this.messageHistory.filter(msg => msg.direction === 'received').length,
      averageMessageSize: this.calculateAverageMessageSize(),
      memoryUsage: this.estimateMemoryUsage()
    };

    // 缓存结果
    this.cache.set('connection-stats', stats);
    return stats;
  }

  /**
   * 更新统计缓存
   */
  updateStatsCache() {
    const stats = this.getStats();
    this.cache.set('connection-stats', stats);
  }

  /**
   * 计算平均消息大小
   */
  calculateAverageMessageSize() {
    if (this.messageHistory.length === 0) return 0;
    
    const totalSize = this.messageHistory.reduce((sum, msg) => sum + (msg.size || 0), 0);
    return Math.round(totalSize / this.messageHistory.length);
  }

  /**
   * 估算内存使用量
   */
  estimateMemoryUsage() {
    const connectionsSize = this.connections.size * 1000; // 估算每个连接1KB
    const historySize = this.messageHistory.length * 500; // 估算每条消息500字节
    return connectionsSize + historySize;
  }

  /**
   * 清理消息历史
   */
  clearMessageHistory() {
    const oldLength = this.messageHistory.length;
    this.messageHistory = [];
    this.logger.info('消息历史已清理:', { oldLength });
    
    this.emit('message-history-cleared');
  }

  /**
   * 移除连接
   */
  removeConnection(connectionId) {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      this.logger.warn('连接不存在:', connectionId);
      return;
    }

    this.connections.delete(connectionId);
    this.logger.info('连接已移除:', { connectionId, url: connection.url });
    
    this.emit('connection-removed', { connectionId, connection });
  }

  /**
   * 发送消息到指定连接
   */
  sendMessage(connectionId, message) {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      this.logger.error('连接不存在:', connectionId);
      return false;
    }

    if (connection.status !== 'connected') {
      this.logger.error('连接未连接:', { connectionId, status: connection.status });
      return false;
    }

    try {
      if (connection.websocket) {
        connection.websocket.send(message);
        this.recordMessage(connectionId, message, 'sent');
        return true;
      } else {
        this.logger.error('WebSocket对象不存在:', connectionId);
        return false;
      }
    } catch (error) {
      this.logger.error('发送消息失败:', { connectionId, error });
      connection.errorCount++;
      this.updateConnectionStatus(connectionId, 'error');
      return false;
    }
  }

  /**
   * 设置消息处理器
   */
  setMessageProcessor(messageProcessor) {
    this.messageProcessor = messageProcessor;
    console.log('[ConnectionManager] 消息处理器已设置');

    // 如果有消息处理器，设置消息处理回调
    if (this.messageProcessor && typeof this.messageProcessor.processMessage === 'function') {
      this.on('message-recorded', (messageData) => {
        try {
          this.messageProcessor.processMessage(messageData);
        } catch (error) {
          this.logger.error('消息处理失败:', error);
        }
      });
    }
  }

  /**
   * 获取消息处理器
   */
  getMessageProcessor() {
    return this.messageProcessor;
  }

  /**
   * 设置消息监听器
   */
  setupMessageListener() {
    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action && request.action.startsWith('connection-')) {
        this.handlePopupMessage(request, sendResponse);
        return true; // 保持异步消息通道打开
      }
    });
  }

  /**
   * 处理popup消息
   */
  handlePopupMessage(request, sendResponse) {
    const { action, data } = request;

    try {
      switch (action) {
        case 'get-connections':
          sendResponse({
            success: true,
            data: this.getAllConnections()
          });
          break;

        case 'get-active-connections':
          sendResponse({
            success: true,
            data: this.getActiveConnections()
          });
          break;

        case 'get-message-history':
          sendResponse({
            success: true,
            data: this.getMessageHistory(data.connectionId, data.limit)
          });
          break;

        case 'get-stats':
          sendResponse({
            success: true,
            data: this.getStats()
          });
          break;

        case 'send-message':
          const success = this.sendMessage(data.connectionId, data.message);
          sendResponse({
            success,
            message: success ? '消息发送成功' : '消息发送失败'
          });
          break;

        case 'clear-history':
          this.clearMessageHistory();
          sendResponse({
            success: true,
            message: '消息历史已清理'
          });
          break;

        case 'remove-connection':
          this.removeConnection(data.connectionId);
          sendResponse({
            success: true,
            message: '连接已移除'
          });
          break;

        default:
          sendResponse({
            success: false,
            error: '未知的操作'
          });
      }
    } catch (error) {
      this.logger.error('处理popup消息失败:', error);
      sendResponse({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 启动清理定时器
   */
  startCleanupTimer() {
    // 每5分钟清理一次过期连接
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredConnections();
    }, 5 * 60 * 1000);
  }

  /**
   * 优化清理过期连接
   */
  cleanupExpiredConnections() {
    const now = Date.now();
    let expiredThreshold = 30 * 60 * 1000; // 30分钟
    
    // 检查内存压力
    if (PerformanceUtils.MemoryMonitor.isMemoryPressure()) {
      this.logger.warn('内存压力较大，进行积极清理');
      // 更短的过期时间
      expiredThreshold = 10 * 60 * 1000; // 10分钟
    }
    
    const expiredConnections = [];
    
    for (const [id, connection] of this.connections) {
      if (connection.status === 'closed' || connection.status === 'error') {
        if (now - connection.lastActivity > expiredThreshold) {
          expiredConnections.push(id);
        }
      }
    }

    if (expiredConnections.length > 0) {
      this.logger.info('清理过期连接:', expiredConnections.length);
      
      expiredConnections.forEach(id => {
        this.removeConnection(id);
      });

      // 清理缓存
      this.cache.clear();
    }

    // 清理过期消息历史
    this.cleanupOldMessages();
  }

  /**
   * 清理旧消息
   */
  cleanupOldMessages() {
    if (this.messageHistory.length > this.maxHistorySize * 1.2) {
      const removeCount = this.messageHistory.length - this.maxHistorySize;
      this.messageHistory.splice(0, removeCount);
      this.logger.info('清理旧消息:', removeCount);
    }
  }

  /**
   * 安排连接清理
   */
  scheduleConnectionCleanup(connectionId) {
    // 5分钟后清理关闭的连接
    setTimeout(() => {
      const connection = this.connections.get(connectionId);
      if (connection && (connection.status === 'closed' || connection.status === 'error')) {
        this.removeConnection(connectionId);
      }
    }, 5 * 60 * 1000);
  }

  /**
   * 导出数据
   */
  exportData() {
    return {
      connections: Array.from(this.connections.values()),
      messageHistory: this.messageHistory,
      stats: this.getStats(),
      timestamp: Date.now()
    };
  }

  /**
   * 生成消息ID
   */
  generateMessageId() {
    return Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 销毁连接管理器（优化版）
   */
  destroy() {
    this.logger.info('销毁连接管理器');
    
    // 清理定时器
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    // 清理批处理器
    this.batchProcessor.clear();
    
    // 清理缓存
    this.cache.clear();
    
    // 清理数据
    this.connections.clear();
    this.messageHistory = [];
    
    // 记录内存使用情况
    PerformanceUtils.MemoryMonitor.logMemoryUsage('ConnectionManager销毁后');
    
    // 移除所有事件监听器
    this.removeAllListeners();
    
    this.initialized = false;
  }
} 