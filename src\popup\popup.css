/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  color: #333;
  background: #f8fafc;
  width: 380px;
  min-height: 500px;
}

.container {
  background: #fff;
  min-height: 500px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 标题栏 */
.header {
  padding: 16px;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header h1 {
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 状态指示器样式 */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
  transition: all 0.3s ease;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.15);
}

.status-indicator::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
  background-color: #ffffff;
  animation: pulse 2s infinite;
}

/* 检测中状态 */
.status-indicator.loading {
  background: rgba(99, 102, 241, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
  color: #ffffff;
  font-weight: 600;
}

.status-indicator.loading::before {
  background-color: #ffffff;
  animation: fastPulse 1s infinite;
}

/* 成功状态 */
.status-indicator.connected {
  background: rgba(34, 197, 94, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.2);
}

.status-indicator.connected::before {
  background-color: #ffffff;
}

/* 失败状态（包括断开连接和错误） */
.status-indicator.disconnected,
.status-indicator.error {
  background: rgba(239, 68, 68, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
}

.status-indicator.disconnected::before,
.status-indicator.error::before {
  background-color: #ffffff;
}

/* 非携程网站状态 */
.status-indicator.not-ctrip-site {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
  font-weight: 500;
}

.status-indicator.not-ctrip-site::before {
  background-color: #ffffff;
}

/* 状态指示器动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(255, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

/* 快速脉冲动画 */
@keyframes fastPulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 状态值样式 */
.value {
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 16px;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
}

.value::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.value.connected {
  color: #16a34a;
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.value.connected::before {
  background-color: #22c55e;
}

.value.disconnected {
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.value.disconnected::before {
  background-color: #ef4444;
}

.value.loading {
  color: #2563eb;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.value.loading::before {
  background-color: #3b82f6;
  animation: pulse 1s infinite;
}

.value.error {
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.value.error::before {
  background-color: #ef4444;
  animation: pulse 0.5s infinite;
}

.value.not-ctrip-site {
  color: #475569;
  background: rgba(100, 116, 139, 0.1);
  border: 1px solid rgba(100, 116, 139, 0.2);
}

.value.not-ctrip-site::before {
  background-color: #64748b;
}



/* 导航标签样式 */
.tabs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  border-bottom: 1px solid #e2e8f0;
  padding: 0 16px;
}

.tab-button {
  padding: 12px 16px;
  border: none;
  background: none;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.tab-button:hover {
  color: #4f46e5;
}

.tab-button.active {
  color: #4f46e5;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: #4f46e5;
  border-radius: 2px;
}

/* 标签页内容样式 */
.tab-content {
  display: none;
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 内容卡片样式 */
.info-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.info-card h3 {
  color: #1e293b;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.info-item .label {
  color: #64748b;
  font-size: 13px;
  font-weight: 500;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  background: #667eea;
  color: white;
}

.btn:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.btn-small {
  padding: 6px 12px;
  font-size: 12px;
}

/* 设置项 */
.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
}

.setting-item input[type="checkbox"] {
  margin: 0;
}

.setting-item select {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
}

/* 拦截设置和自动回复设置统一样式 */
.interceptor-setting,
.auto-reply-setting {
  flex-direction: column;
  align-items: flex-start;
  padding: 12px 0;
}

.setting-main {
  width: 100%;
  margin-bottom: 8px;
}

/* 统一的设置标签样式 */
.setting-label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  color: #374151;
}

.setting-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
}

/* 描述文字 */
.auto-reply-setting .setting-main {
  margin-bottom: 8px;
}

.setting-description {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
  margin-left: 28px;
  font-style: italic;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 32px 16px;
  color: #666;
  font-style: italic;
}

/* 底部栏 */
.footer {
  padding: 12px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.version {
  font-size: 11px;
  color: #888;
}

/* 滚动条样式 */
.templates-list::-webkit-scrollbar {
  width: 6px;
}

.templates-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.templates-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.templates-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 模板管理样式 */
.template-management {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.template-management h3 {
  margin-bottom: 16px;
  flex-shrink: 0;
}

/* 顶部添加模板按钮 - 占满宽度 */
.template-add-section {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.btn-add-template {
  width: 100%;
  padding: 14px 20px;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-add-template .add-icon {
  color: white;
  font-size: 20px;
  font-weight: 300;
  line-height: 1;
  text-shadow: none;
}

.btn-add-template:hover {
  background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.btn-add-template:hover .add-icon {
  color: white;
}

/* 中间导入导出按钮 */
.template-import-export {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.btn-import-export {
  flex: 1;
  padding: 10px 16px;
  background: #f3f4f6;
  color: #4b5563;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-import-export:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

/* 底部搜索框 */
.template-search-section {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.template-search-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.template-search-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 模板列表容器 */
.templates-list {
  flex: 1;
  overflow-y: auto;
  min-height: 200px;
}

/* 模板项样式 */
.template-item {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.template-item:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.template-item.disabled {
  opacity: 0.6;
  background: #f9fafb;
}

/* 模板内容 - 垂直布局 */
.template-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 模板文本 - 占一行 */
.template-text {
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
  word-break: break-word;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

/* 模板操作按钮 */
.template-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.template-action-btn {
  padding: 8px 10px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  justify-content: center;
}

.template-action-btn.edit {
  background: #f3f4f6;
  color: #4b5563;
  border: 1px solid #d1d5db;
}

.template-action-btn.edit:hover {
  background: #e5e7eb;
  color: #374151;
  border-color: #9ca3af;
}

.template-action-btn.delete {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.template-action-btn.delete:hover {
  background: #fee2e2;
  color: #b91c1c;
  border-color: #f87171;
}

.template-action-btn.toggle {
  background: #f0fdf4;
  color: #16a34a;
  border: 1px solid #bbf7d0;
}

.template-action-btn.toggle:hover {
  background: #dcfce7;
  color: #15803d;
  border-color: #86efac;
}

.template-action-btn.toggle.disabled {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.template-action-btn.toggle.disabled:hover {
  background: #fee2e2;
  color: #b91c1c;
  border-color: #f87171;
}

.btn-primary {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.btn-secondary {
  background: #6b7280;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: #4b5563;
  transform: translateY(-1px);
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

.modal-content {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 520px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  animation: modalSlideIn 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  padding: 24px;
  border-bottom: 2px solid #f3f4f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  flex-shrink: 0;
}

.modal-header h3 {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
  letter-spacing: -0.025em;
}

.modal-close {
  background: none;
  border: none;
  font-size: 28px;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f3f4f6;
  color: #374151;
  transform: scale(1.1);
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

.modal-footer {
  padding: 20px 24px;
  border-top: 2px solid #f3f4f6;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  background: #f9fafb;
  flex-shrink: 0;
}

/* 表单样式 */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  font-family: inherit;
  box-sizing: border-box;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  background: #fefefe;
}

.form-select {
  background-color: white;
  cursor: pointer;
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
  line-height: 1.6;
}

.form-hint {
  margin-top: 6px;
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  font-weight: 500 !important;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.checkbox-label:hover {
  background: #f3f4f6;
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
}

/* 响应式调整 */
@media (max-width: 400px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    max-height: 95vh;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 16px;
  }

  .modal-footer {
    flex-direction: column;
  }
}