// 携程IM WebSocket代理 - 专门用于创建携程IM的WebSocket连接

(function() {
  'use strict';

  console.log('[CtripIM WebSocket] 🚀 携程IM WebSocket代理初始化...');
  console.log('[CtripIM WebSocket] 🌍 当前执行环境: MAIN WORLD');
  console.log('[CtripIM WebSocket] 📍 当前URL:', window.location.href);
  console.log('[CtripIM WebSocket] 📅 加载时间:', new Date().toLocaleTimeString());

  // 检查是否已经安装
  if (window._ctripWebSocketInterceptorInstalled) {
    console.log('[CtripIM WebSocket] ⚠️ 携程IM WebSocket代理已安装，跳过');
    return;
  }

  // 保存原始WebSocket
  const OriginalWebSocket = window.WebSocket;
  if (!OriginalWebSocket) {
    console.log('[CtripIM WebSocket] ❌ WebSocket不可用');
    return;
  }

  console.log('[CtripIM WebSocket] 💾 原始WebSocket已保存:', OriginalWebSocket.name);

  // 存储用户配置的模板
  let userTemplates = [];
  let templatesLastUpdated = 0;

  // 直接从存储获取模板的备用方法
  async function getTemplatesFromStorage() {
    try {
      console.log('[CtripIM WebSocket] 🔄 尝试直接从存储获取模板...');

      // 方法1：尝试通过content script获取
      if (window.ctripIMContentScript && window.ctripIMContentScript.autoReplyEngine) {
        console.log('[CtripIM WebSocket] 🔄 尝试通过Content Script获取模板...');
        try {
          const templates = window.ctripIMContentScript.autoReplyEngine.getAllTemplates();
          if (templates && templates.length > 0) {
            console.log('[CtripIM WebSocket] ✅ 通过Content Script获取到模板:', templates.length, '个');
            return templates;
          }
        } catch (error) {
          console.warn('[CtripIM WebSocket] ⚠️ 通过Content Script获取模板失败:', error);
        }
      }

      // 方法2：尝试通过全局API获取
      if (window.ctripIM && window.ctripIM.getTemplates) {
        console.log('[CtripIM WebSocket] 🔄 尝试通过全局API获取模板...');
        try {
          const templates = await window.ctripIM.getTemplates();
          if (templates && templates.length > 0) {
            console.log('[CtripIM WebSocket] ✅ 通过全局API获取到模板:', templates.length, '个');
            return templates;
          }
        } catch (error) {
          console.warn('[CtripIM WebSocket] ⚠️ 通过全局API获取模板失败:', error);
        }
      }

      // 方法3：通过页面脚本访问存储（在ISOLATED世界中执行）
      console.log('[CtripIM WebSocket] 🔄 尝试通过页面脚本获取存储...');
      return new Promise((resolve) => {
        // 发送请求给content script
        window.postMessage({
          type: 'ctripGetStorageTemplates',
          source: 'websocket-interceptor',
          timestamp: Date.now()
        }, '*');

        // 监听响应
        const storageHandler = (event) => {
          if (event.data?.type === 'ctripStorageTemplatesResponse' && event.data?.source === 'content-script') {
            window.removeEventListener('message', storageHandler);
            console.log('[CtripIM WebSocket] 📦 收到存储模板响应:', event.data.data);
            resolve(event.data.data.templates || []);
          }
        };

        window.addEventListener('message', storageHandler);

        // 5秒超时
        setTimeout(() => {
          window.removeEventListener('message', storageHandler);
          console.warn('[CtripIM WebSocket] ⏰ 存储模板请求超时');
          resolve([]);
        }, 5000);
      });
    } catch (error) {
      console.error('[CtripIM WebSocket] ❌ 直接获取存储模板失败:', error);
      return [];
    }
  }

  // 请求用户配置的模板
  function requestUserTemplates() {
    console.log('[CtripIM WebSocket] 📤 请求用户模板...');
    console.log('[CtripIM WebSocket] 🔍 检查content-script状态:', {
      hasContentScript: !!window.ctripIMContentScript,
      hasGlobalAPI: !!window.ctripIM,
      documentReady: document.readyState,
      hasReadyMarker: !!document.getElementById('ctrip-content-script-ready'),
      bodyExists: !!document.body,
      currentTime: new Date().toLocaleTimeString()
    });

    // 发送模板请求消息
    const requestMessage = {
      type: 'ctripRequestTemplates',
      source: 'websocket-interceptor',
      timestamp: Date.now()
    };
    
    console.log('[CtripIM WebSocket] 📤 发送模板请求消息:', requestMessage);
    
    // 使用多种方式发送请求
    window.postMessage(requestMessage, '*');
    
    // 也通过DOM事件发送
    if (document.body) {
      document.dispatchEvent(new CustomEvent('ctripRequestTemplates', {
        detail: requestMessage
      }));
    }
  }

  // 监听模板响应和content-script就绪通知
  window.addEventListener('message', (event) => {
    console.log('[CtripIM WebSocket] 📨 收到window.postMessage:', event.data?.type, 'from:', event.data?.source);
    
    // 处理content-script就绪通知
    if (event.data?.type === 'ctripContentScriptReady' && event.data?.source === 'content-script') {
      console.log('[CtripIM WebSocket] 🎉 Content Script已就绪(postMessage)，立即请求模板');
      requestUserTemplates();
      return;
    }

    // 处理模板响应
    if (event.data?.type === 'ctripTemplatesResponse' && event.data?.source === 'content-script') {
      userTemplates = event.data.data.templates || [];
      templatesLastUpdated = Date.now();
      console.log('[CtripIM WebSocket] 📋 已更新用户模板:', userTemplates.length, '个');

      // 详细输出模板信息
      if (userTemplates.length > 0) {
        console.log('[CtripIM WebSocket] 📋 模板详情:', userTemplates.map(t => ({
          id: t.id,
          name: t.name,
          enabled: t.enabled,
          keywords: t.keywords,
          category: t.category
        })));
      } else {
        console.warn('[CtripIM WebSocket] ⚠️ 收到空的模板列表');
      }
    }

    // 处理存储模板响应
    if (event.data?.type === 'ctripStorageTemplatesResponse' && event.data?.source === 'content-script') {
      const templates = event.data.data?.templates || [];
      if (templates.length > 0 && userTemplates.length === 0) {
        userTemplates = templates;
        templatesLastUpdated = Date.now();
        console.log('[CtripIM WebSocket] 📦 从存储获取到模板:', userTemplates.length, '个');
      }
    }
  });

  // 监听自定义DOM事件
  document.addEventListener('ctripContentScriptReady', (event) => {
    console.log('[CtripIM WebSocket] 🎉 Content Script已就绪(CustomEvent)，立即请求模板');
    requestUserTemplates();
  });

  // 监听模板响应的DOM事件
  document.addEventListener('ctripTemplatesResponse', (event) => {
    console.log('[CtripIM WebSocket] 📋 收到模板响应(CustomEvent)');
    const eventData = event.detail;
    if (eventData && eventData.data) {
      userTemplates = eventData.data.templates || [];
      templatesLastUpdated = Date.now();
      console.log('[CtripIM WebSocket] 📋 通过CustomEvent更新用户模板:', userTemplates.length, '个');
      
      // 详细输出模板信息
      if (userTemplates.length > 0) {
        console.log('[CtripIM WebSocket] 📋 模板详情:', userTemplates.map(t => ({
          id: t.id,
          name: t.name,
          enabled: t.enabled,
          keywords: t.keywords,
          category: t.category
        })));
      }
    }
  });

  // 定期检查DOM标记
  let domCheckInterval = setInterval(() => {
    const readyMarker = document.getElementById('ctrip-content-script-ready');
    if (readyMarker) {
      console.log('[CtripIM WebSocket] 🎉 Content Script已就绪(DOM检查)，立即请求模板');
      clearInterval(domCheckInterval);
      requestUserTemplates();
    }
  }, 1000); // 每秒检查一次

  // 10秒后停止DOM检查
  setTimeout(() => {
    if (domCheckInterval) {
      clearInterval(domCheckInterval);
      console.log('[CtripIM WebSocket] ⏰ DOM检查超时，停止检查');
    }
  }, 10000);

  // 尝试请求模板（带重试机制）
  let templateRequestRetries = 0;
  const MAX_TEMPLATE_RETRIES = 10; // 增加到10次重试
  const TEMPLATE_RETRY_INTERVAL = 2000; // 2秒间隔

  function tryRequestTemplates() {
    if (templateRequestRetries >= MAX_TEMPLATE_RETRIES) {
      console.error('[CtripIM WebSocket] ❌ 模板请求失败，已达到最大重试次数');
      return;
    }

    templateRequestRetries++;
    console.log(`[CtripIM WebSocket] 🔄 第 ${templateRequestRetries} 次尝试请求模板...`);
    
    requestUserTemplates();
    
    // 如果还没有模板，继续重试
    setTimeout(() => {
      if (userTemplates.length === 0 && templateRequestRetries < MAX_TEMPLATE_RETRIES) {
        tryRequestTemplates();
      }
    }, TEMPLATE_RETRY_INTERVAL);
  }

  // 延迟启动模板请求，等待content script加载
  setTimeout(() => {
    console.log('[CtripIM WebSocket] ⏰ 开始请求用户模板...');
    tryRequestTemplates();
  }, 3000); // 延迟3秒

  // 额外的备用请求机制
  setTimeout(() => {
    if (userTemplates.length === 0) {
      console.log('[CtripIM WebSocket] 🔄 第一次备用模板请求...');
      tryRequestTemplates();
    }
  }, 8000); // 8秒后再次尝试

  // 最后的备用请求
  setTimeout(() => {
    if (userTemplates.length === 0) {
      console.log('[CtripIM WebSocket] 🔄 最后一次备用模板请求...');
      tryRequestTemplates();
    }
  }, 15000); // 15秒后最后尝试

  // 智能自动回复生成函数 - 严格按照用户配置的模板
  function generateAutoReply(messageContent) {
    if (!messageContent || typeof messageContent !== 'string') {
      return null;
    }

    const content = messageContent.toLowerCase();

    // 如果模板太旧，重新请求
    if (Date.now() - templatesLastUpdated > 30000) { // 30秒
      requestUserTemplates();
    }

    console.log('[CtripIM WebSocket] 🔍 开始匹配用户模板:', {
      userTemplates: userTemplates,
      userTemplatesCount: userTemplates.length,
      messageContent: messageContent,
      enabledTemplatesCount: userTemplates.filter(t => t.enabled).length
    });

    // 只使用用户配置的模板，严格按照业务逻辑
    if (userTemplates.length > 0) {
      // 第一步：关键词精确匹配（只处理已启用的模板）
      for (const template of userTemplates) {
        if (template.enabled === true && template.keywords && Array.isArray(template.keywords) && template.keywords.length > 0) {
          for (const keyword of template.keywords) {
            if (keyword && typeof keyword === 'string' && content.includes(keyword.toLowerCase())) {
              console.log('[CtripIM WebSocket] 🎯 关键词匹配成功:', {
                keyword: keyword,
                templateName: template.name,
                templateId: template.id,
                category: template.category
              });
              return template.content;
            }
          }
        }
      }

      console.log('[CtripIM WebSocket] ℹ️ 没有关键词匹配，尝试使用通用模板');

      // 第二步：如果没有关键词匹配，使用通用模板
      // 优先选择 general 分类的已启用模板
      const generalTemplates = userTemplates.filter(t => 
        t.enabled === true && t.category === 'general'
      );
      
      if (generalTemplates.length > 0) {
        const template = generalTemplates[Math.floor(Math.random() * generalTemplates.length)];
        console.log('[CtripIM WebSocket] 📝 使用通用模板:', {
          templateName: template.name,
          templateId: template.id,
          category: template.category
        });
        return template.content;
      }

      // 第三步：如果没有通用模板，使用任意已启用的模板
      const enabledTemplates = userTemplates.filter(t => t.enabled === true);
      if (enabledTemplates.length > 0) {
        const template = enabledTemplates[Math.floor(Math.random() * enabledTemplates.length)];
        console.log('[CtripIM WebSocket] 📝 使用随机已启用模板:', {
          templateName: template.name,
          templateId: template.id,
          category: template.category
        });
        return template.content;
      }
    }

    // 兜底回复：没有匹配到任何用户配置的模板
    console.log('[CtripIM WebSocket] 📝 使用兜底回复 - 没有匹配的用户模板');
    return '您好！感谢您的消息，我们会尽快回复您。';
  }

  // 代理数据存储
  const proxyData = {
    activeConnections: new Map(),
    connectionStats: {
      totalConnections: 0,
      activeConnections: 0,
      messagesReceived: 0,
      messagesSent: 0,
      lastActivity: null
    },
    messageHandlers: new Set(),
    installTime: Date.now(),
    // 防重复回复
    processedMessages: new Set(),
    lastReplyTime: 0,
    replyInterval: 3000 // 3秒内不重复回复
  };

  // 生成连接ID
  function generateConnectionId() {
    return 'ws_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // 生成消息ID
  function generateMessageId() {
    return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // 处理消息
  function handleMessage(connectionId, data, direction) {
    try {
      const connectionInfo = proxyData.activeConnections.get(connectionId);
      if (!connectionInfo) return;

      // 更新统计
      if (direction === 'received') {
        proxyData.connectionStats.messagesReceived++;
        
        // 检查是否是用户发来的消息
        if (typeof data === 'string' && data.includes('type="groupchat"')) {
          console.log('[CtripIM WebSocket] 🔍 检测到groupchat消息:', data.substring(0, 200) + '...');

          try {
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(data, 'text/xml');
            const messageElement = xmlDoc.querySelector('message');

            // 检查是否是群聊消息（支持多种类型）
            const messageType = messageElement?.getAttribute('type');
            const realChatType = messageElement?.getAttribute('real_chat_type');
            const isGroupChat = messageType === 'groupchat' || realChatType === 'groupchat';

            console.log('[CtripIM WebSocket] 📋 消息元素分析:', {
              hasMessageElement: !!messageElement,
              messageType: messageType,
              realChatType: realChatType,
              isGroupChat: isGroupChat,
              hasBody: !!messageElement?.querySelector('body'),
              from: messageElement?.getAttribute('from'),
              to: messageElement?.getAttribute('to')
            });

            if (messageElement && isGroupChat && messageElement.querySelector('body')) {

              // 获取消息ID用于防重复
              const messageId = messageElement.getAttribute('id');
              const msgType = messageElement.getAttribute('msgtype');

              // 只处理用户发送的文本消息（msgtype="0"表示文本消息）
              if (msgType !== '0') {
                console.log('[CtripIM WebSocket] ℹ️ 跳过非文本消息:', { messageId, msgType });
                return;
              }

              // 防重复处理
              if (messageId && proxyData.processedMessages.has(messageId)) {
                console.log('[CtripIM WebSocket] ℹ️ 消息已处理，跳过:', messageId);
                return;
              }

              // 防频繁回复
              const now = Date.now();
              if (now - proxyData.lastReplyTime < proxyData.replyInterval) {
                console.log('[CtripIM WebSocket] ℹ️ 回复间隔太短，跳过');
                return;
              }

              // 获取必要的信息
              const threadId = messageElement.getAttribute('threadid');
              const toAttr = messageElement.getAttribute('to');
              const fromAttr = messageElement.getAttribute('from');
              const toJid = toAttr ? toAttr.split('/')[0] : null;
              const fromJid = fromAttr ? fromAttr.split('/')[0] : null;
              const bodyElement = messageElement.querySelector('body');

              // 验证必要字段
              if (!fromJid || !toJid || !bodyElement) {
                console.warn('[CtripIM WebSocket] ⚠️ 消息缺少必要字段:', {
                  hasFromJid: !!fromJid,
                  hasToJid: !!toJid,
                  hasBody: !!bodyElement,
                  threadId: threadId
                });
                return;
              }

              // 解析消息内容
              let messageContent = '';
              try {
                const bodyText = bodyElement.textContent || bodyElement.innerText || '';
                if (bodyText) {
                  try {
                    const bodyData = JSON.parse(bodyText);
                    messageContent = bodyData.msg || bodyText;
                  } catch (jsonError) {
                    messageContent = bodyText;
                  }
                }
              } catch (e) {
                console.warn('[CtripIM WebSocket] ⚠️ 解析消息内容失败:', e);
                messageContent = '';
              }

              // 如果没有消息内容，跳过处理
              if (!messageContent.trim()) {
                console.debug('[CtripIM WebSocket] ℹ️ 消息内容为空，跳过自动回复');
                return;
              }

              // 直接在拦截器中处理自动回复（避免跨世界通信问题）
              const autoReply = generateAutoReply(messageContent);

              if (autoReply) {
                // 记录已处理的消息
                if (messageId) {
                  proxyData.processedMessages.add(messageId);
                  // 限制缓存大小，只保留最近100条
                  if (proxyData.processedMessages.size > 100) {
                    const firstItem = proxyData.processedMessages.values().next().value;
                    proxyData.processedMessages.delete(firstItem);
                  }
                }

                // 更新最后回复时间
                proxyData.lastReplyTime = Date.now();

                // 构造回复消息
                const replyId = 'reply_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                const replyMessage = `<message to="${fromJid}/csm0000001004960" type="send" id="${replyId}" xmlns="jabber:client"><body>{"chattype":"groupchat","biztype":"2107","localid":"${replyId}","msg":"${autoReply}","msgtype":"0","autoextend":0,"threadid":"${threadId}"}</body></message>`;

                // 直接发送回复
                if (connectionInfo.websocket && connectionInfo.websocket.readyState === WebSocket.OPEN) {
                  connectionInfo.websocket.send(replyMessage);
                  console.log('[CtripIM WebSocket] 🚀 已发送自动回复:', autoReply);
                } else {
                  console.warn('[CtripIM WebSocket] ⚠️ WebSocket连接不可用');
                }
              }

              console.log('[CtripIM WebSocket] 📨 已发送自动回复请求:', {
                fromJid: fromJid,
                content: messageContent,
                threadId: threadId
              });
            }
          } catch (error) {
            console.error('[CtripIM WebSocket] ❌ 解析XML消息失败:', error);
          }
        }
      } else {
        proxyData.connectionStats.messagesSent++;
      }

      proxyData.connectionStats.lastActivity = Date.now();
      connectionInfo.lastActivity = Date.now();
      connectionInfo.messageCount++;

      console.log(`[CtripIM WebSocket] 📨 ${direction === 'sent' ? '发送' : '接收'}消息:`, {
        connectionId: connectionId,
        url: connectionInfo.url,
        dataLength: data?.length || 0,
        data: typeof data === 'string' ? data.slice(0, 100) : '非文本数据' // 只显示前100个字符
      });

      // 创建消息数据
      const messageData = {
        id: generateMessageId(),
        connectionId: connectionId,
        message: data,
        direction: direction,
        timestamp: Date.now(),
        isCtripProxy: true,
        source: 'ctrip_websocket_proxy'
      };

      // 触发自定义事件
      window.dispatchEvent(new CustomEvent('ctripWebSocketMessage', {
        detail: messageData
      }));

    } catch (error) {
      console.error('[CtripIM WebSocket] ❌ 处理消息失败:', error);
    }
  }

  // 更新连接状态
  function updateConnectionStatus(connectionId, status) {
    const connectionInfo = proxyData.activeConnections.get(connectionId);
    if (connectionInfo) {
      connectionInfo.status = status;
      connectionInfo.lastActivity = Date.now();

      console.log('[CtripIM WebSocket] � 连接状态更新:', {
        connectionId,
        status,
        url: connectionInfo.url,
        protocol: connectionInfo.protocols
      });

      // 触发状态变化事件
      window.dispatchEvent(new CustomEvent('ctripWebSocketStatusChange', {
        detail: {
          connectionId: connectionId,
          status: status,
          url: connectionInfo.url,
          timestamp: Date.now()
        }
      }));
    }
  }

  // 设置WebSocket事件监听器
  function setupWebSocketEventListeners(websocket, connectionId) {
    const connectionInfo = proxyData.activeConnections.get(connectionId);
    let heartbeatInterval = null;
    let lastPongTime = null;

    // 心跳检测函数
    function startHeartbeat() {
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
      }
      
      heartbeatInterval = setInterval(() => {
        try {
          if (websocket.readyState === WebSocket.OPEN) {
            websocket.send(JSON.stringify({
              type: 'ping',
              timestamp: Date.now()
            }));
            
            // 如果超过10秒没有收到pong响应，认为连接有问题
            if (lastPongTime && Date.now() - lastPongTime > 10000) {
              console.log('[CtripIM WebSocket] ⚠️ 心跳检测超时:', {
                connectionId,
                lastPongTime: new Date(lastPongTime).toISOString()
              });
              updateConnectionStatus(connectionId, 'error');
            }
          }
        } catch (error) {
          console.error('[CtripIM WebSocket] ❌ 发送心跳消息失败:', error);
          updateConnectionStatus(connectionId, 'error');
        }
      }, 30000); // 每30秒发送一次心跳
    }

    // 拦截发送方法
    const originalSend = websocket.send;
    websocket.send = function (data) {
      handleMessage(connectionId, data, 'sent');
      return originalSend.call(this, data);
    };

    // 拦截消息事件
    const originalAddEventListener = websocket.addEventListener;
    websocket.addEventListener = function (type, listener, options) {
      if (type === 'message') {
        const wrappedListener = function (event) {
          // 处理pong响应
          if (typeof event.data === 'string') {
            try {
              const data = JSON.parse(event.data);
              if (data.type === 'pong') {
                lastPongTime = Date.now();
                return;
              }
            } catch (e) {
              // 不是JSON或不是pong消息，当作普通消息处理
            }
          }
          
          handleMessage(connectionId, event.data, 'received');
          return listener.call(this, event);
        };
        return originalAddEventListener.call(this, type, wrappedListener, options);
      }
      return originalAddEventListener.call(this, type, listener, options);
    };

    // 拦截onmessage属性
    let originalOnMessage = websocket.onmessage;
    Object.defineProperty(websocket, 'onmessage', {
      get: () => originalOnMessage,
      set: (handler) => {
        originalOnMessage = handler;
        websocket.onmessage = (event) => {
          // 处理pong响应
          if (typeof event.data === 'string') {
            try {
              const data = JSON.parse(event.data);
              if (data.type === 'pong') {
                lastPongTime = Date.now();
                return;
              }
            } catch (e) {
              // 不是JSON或不是pong消息，当作普通消息处理
            }
          }
          
          handleMessage(connectionId, event.data, 'received');
          if (handler) handler(event);
        };
      }
    });

    // 监听连接状态
    websocket.addEventListener('open', function () {
      console.log('[CtripIM WebSocket] ✅ WebSocket连接已打开:', {
        url: connectionInfo?.url,
        protocol: websocket.protocol,
        extensions: websocket.extensions,
        readyState: websocket.readyState,
        connectionId: connectionId,
        timestamp: Date.now()
      });
      
      // 更新连接状态
      updateConnectionStatus(connectionId, 'connected');
      
      // 启动心跳检测
      startHeartbeat();
      
      // 立即发送一个测试消息来验证连接
      try {
        websocket.send(JSON.stringify({
          type: 'ping',
          timestamp: Date.now()
        }));
        console.log('[CtripIM WebSocket] 🏓 发送测试消息成功');
      } catch (error) {
        console.error('[CtripIM WebSocket] ❌ 发送测试消息失败:', error);
        updateConnectionStatus(connectionId, 'error');
      }
    });

    websocket.addEventListener('close', function (event) {
      console.log('[CtripIM WebSocket] ❌ WebSocket连接已关闭:', {
        url: connectionInfo?.url,
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean
      });
      
      // 清理心跳检测
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
        heartbeatInterval = null;
      }
      
      updateConnectionStatus(connectionId, 'closed');

      // 更新活跃连接数
      if (proxyData.connectionStats.activeConnections > 0) {
        proxyData.connectionStats.activeConnections--;
      }
    });

    websocket.addEventListener('error', function (error) {
      console.log('[CtripIM WebSocket] 🚫 WebSocket连接错误:', {
        url: connectionInfo?.url,
        error: error
      });
      
      // 清理心跳检测
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
        heartbeatInterval = null;
      }
      
      updateConnectionStatus(connectionId, 'error');
    });
  }

  // 创建WebSocket代理
  function createWebSocketProxy(url, protocols) {
    const connectionId = generateConnectionId();
    
    console.log('[CtripIM WebSocket] 🔍 创建新的WebSocket连接:', {
      url,
      protocols,
      connectionId,
      origin: window.location.origin,
      host: new URL(url).host
    });

    // 创建原始WebSocket连接
    const originalWebSocket = new OriginalWebSocket(url, protocols);

    // 更新统计信息
    proxyData.connectionStats.totalConnections++;
    proxyData.connectionStats.activeConnections++;
    proxyData.connectionStats.lastActivity = Date.now();

    // 存储连接信息
    const connectionInfo = {
      id: connectionId,
      url: url,
      protocols: protocols,
      status: 'connecting',
      createdAt: Date.now(),
      lastActivity: Date.now(),
      messageCount: 0,
      websocket: originalWebSocket,
      source: 'ctrip_proxy'
    };

    proxyData.activeConnections.set(connectionId, connectionInfo);

    // 设置事件监听器
    setupWebSocketEventListeners(originalWebSocket, connectionId);

    return originalWebSocket;
  }

  // 复制原始WebSocket的属性和方法
  try {
    Object.setPrototypeOf(createWebSocketProxy.prototype, OriginalWebSocket.prototype);
    Object.setPrototypeOf(createWebSocketProxy, OriginalWebSocket);

    // 复制静态属性（跳过只读常量）
    const readOnlyProperties = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
    for (const key in OriginalWebSocket) {
      if (OriginalWebSocket.hasOwnProperty(key) && !readOnlyProperties.includes(key)) {
        try {
          const descriptor = Object.getOwnPropertyDescriptor(OriginalWebSocket, key);
          if (descriptor && descriptor.writable !== false) {
            createWebSocketProxy[key] = OriginalWebSocket[key];
          }
        } catch (e) {
          // 忽略只读属性错误
          console.debug('[CtripIM WebSocket] 跳过只读属性:', key, e.message);
        }
      }
    }

    // 使用Object.defineProperty安全地设置WebSocket常量
    try {
      Object.defineProperty(createWebSocketProxy, 'CONNECTING', {
        value: 0,
        writable: false,
        enumerable: true,
        configurable: false
      });
      Object.defineProperty(createWebSocketProxy, 'OPEN', {
        value: 1,
        writable: false,
        enumerable: true,
        configurable: false
      });
      Object.defineProperty(createWebSocketProxy, 'CLOSING', {
        value: 2,
        writable: false,
        enumerable: true,
        configurable: false
      });
      Object.defineProperty(createWebSocketProxy, 'CLOSED', {
        value: 3,
        writable: false,
        enumerable: true,
        configurable: false
      });
    } catch (e) {
      console.warn('[CtripIM WebSocket] ⚠️ 设置WebSocket常量时出错:', e.message);
      // 如果defineProperty失败，尝试直接赋值（可能在某些环境中有效）
      try {
        createWebSocketProxy.CONNECTING = 0;
        createWebSocketProxy.OPEN = 1;
        createWebSocketProxy.CLOSING = 2;
        createWebSocketProxy.CLOSED = 3;
      } catch (e2) {
        console.warn('[CtripIM WebSocket] ⚠️ 直接赋值WebSocket常量也失败:', e2.message);
      }
    }

  } catch (e) {
    console.error('[CtripIM WebSocket] ❌ 初始化WebSocket代理时出错:', e);
    // 如果代理初始化失败，确保原始WebSocket仍然可用
    if (!window.WebSocket) {
      window.WebSocket = OriginalWebSocket;
    }
    return false; // 表示初始化失败
  }

  // 替换全局WebSocket
  window.WebSocket = createWebSocketProxy;

  // 验证替换是否成功
  console.log('[CtripIM WebSocket] 🔄 WebSocket已替换为代理函数:', {
    isProxy: window.WebSocket === createWebSocketProxy,
    name: window.WebSocket.name,
    prototype: Object.getPrototypeOf(window.WebSocket)
  });

  // 标记已安装并暴露API
  window._ctripWebSocketInterceptorInstalled = true;
  window._ctripWebSocketProxyData = proxyData;
  window._ctripWebSocketInterceptor = {
    getConnection: (connectionId) => proxyData.activeConnections.get(connectionId),
    getAllConnections: () => Array.from(proxyData.activeConnections.values()),
    getDebugInfo: () => ({
      activeConnections: proxyData.activeConnections.size,
      totalConnections: proxyData.connectionStats.totalConnections,
      lastActivity: proxyData.connectionStats.lastActivity
    })
  };

  console.log('[CtripIM WebSocket] ✅ WebSocket代理初始化完成');
  return true; // 表示初始化成功

})(); 