<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>携程IM拦截器</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="container">
    <!-- 标题栏 -->
    <header class="header">
      <h1>携程IM拦截器</h1>
      <div class="status-indicator" id="statusIndicator">
        <span class="status-text">未连接</span>
      </div>
    </header>

    <!-- 导航标签 -->
    <nav class="tabs">
      <button class="tab-button active" data-tab="status">状态</button>
      <button class="tab-button" data-tab="messages">消息</button>
      <button class="tab-button" data-tab="settings">设置</button>
    </nav>

    <!-- 状态页面 -->
    <div class="tab-content active" id="statusTab">
      <div class="info-card">
        <h3>扩展状态</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">状态:</span>
            <span class="value" id="extensionStatus">检测中...</span>
          </div>
          <div class="info-item">
            <span class="label">活跃连接:</span>
            <span class="value" id="activeConnections">0</span>
          </div>
          <div class="info-item">
            <span class="label">总连接数:</span>
            <span class="value" id="totalConnections">0</span>
          </div>
          <div class="info-item">
            <span class="label">消息数量:</span>
            <span class="value" id="messageCount">0</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 消息模板页面 -->
    <div class="tab-content" id="messagesTab">
      <div class="info-card template-management">
        <h3>消息模板管理</h3>

        <!-- 顶部添加模板按钮 - 占满宽度 -->
        <div class="template-add-section">
          <button class="btn-add-template" id="addTemplate">
            <span class="add-icon">+</span>
            添加模板
          </button>
        </div>

        <!-- 中间导入导出按钮 -->
        <div class="template-import-export">
          <button class="btn-import-export" id="importTemplates">
            <span>📥</span>
            导入模板
          </button>
          <button class="btn-import-export" id="exportTemplates">
            <span>📤</span>
            导出模板
          </button>
        </div>

        <!-- 底部搜索框 -->
        <div class="template-search-section">
          <select id="templateCategory" class="template-search-select">
            <option value="">所有分类</option>
            <option value="greeting">问候语</option>
            <option value="inquiry">咨询回复</option>
            <option value="complaint">投诉处理</option>
            <option value="general">通用回复</option>
            <option value="custom">自定义</option>
          </select>
        </div>

        <!-- 模板列表 -->
        <div class="templates-list" id="templatesList">
          <div class="empty-state">暂无模板，点击"添加模板"开始创建</div>
        </div>
      </div>
    </div>

    <!-- 模板编辑弹窗 -->
    <div class="modal" id="templateModal" style="display: none;">
      <div class="modal-content">
        <div class="modal-header">
          <h3 id="modalTitle">添加消息模板</h3>
          <button class="modal-close" id="closeModal">&times;</button>
        </div>
        <div class="modal-body">
          <form id="templateForm">
            <div class="form-group">
              <label for="templateName">模板名称:</label>
              <input type="text" id="templateName" class="form-input" placeholder="请输入模板名称" required>
            </div>

            <div class="form-group">
              <label for="templateCategoryEdit">分类:</label>
              <select id="templateCategoryEdit" class="form-select" required>
                <option value="greeting">问候语</option>
                <option value="inquiry">咨询回复</option>
                <option value="complaint">投诉处理</option>
                <option value="general">通用回复</option>
                <option value="custom">自定义</option>
              </select>
            </div>

            <div class="form-group">
              <label for="templateContent">模板内容:</label>
              <textarea id="templateContent" class="form-textarea" placeholder="请输入回复内容..." rows="4" required></textarea>
              <div class="form-hint">支持变量: {用户名}, {时间}, {日期}</div>
            </div>

            <div class="form-group">
              <label for="templateKeywords">触发关键词:</label>
              <input type="text" id="templateKeywords" class="form-input" placeholder="多个关键词用逗号分隔">
              <div class="form-hint">当消息包含这些关键词时会触发此模板</div>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" id="templateEnabled" checked>
                启用此模板
              </label>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn-secondary" id="cancelTemplate">取消</button>
          <button type="submit" class="btn-primary" id="saveTemplate" form="templateForm">保存</button>
        </div>
      </div>
    </div>

    <!-- 设置页面 -->
    <div class="tab-content" id="settingsTab">
      <div class="info-card">
        <h3>拦截设置</h3>
        <div class="setting-item interceptor-setting">
          <div class="setting-main">
            <label class="setting-label">
              <input type="checkbox" id="enableInterceptor" checked>
              启用WebSocket拦截
            </label>
          </div>
          <div class="setting-description">开启后将拦截所有WebSocket连接，可转发消息到该处</div>
        </div>
        <div class="setting-item auto-reply-setting">
          <div class="setting-main">
            <label class="setting-label">
              <input type="checkbox" id="enableAutoReply">
              启用自动回复
            </label>
          </div>
          <div class="setting-description">开启后将根据配置的消息模板自动回复客户消息</div>
        </div>
        <div class="auto-reply-status" id="autoReplyStatusCard" style="display: none;">
          <div class="status-item">
            <span class="label">活跃模板:</span>
            <span class="value" id="activeTemplatesCount">0</span>
          </div>
          <div class="status-item">
            <span class="label">今日回复:</span>
            <span class="value" id="todayRepliesCount">0</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <footer class="footer">
      <button class="btn-small" id="refreshData">刷新数据</button>
      <span class="version">v1.0.3</span>
    </footer>
  </div>

  <script src="popup-manager.js"></script>
</body>
</html> 