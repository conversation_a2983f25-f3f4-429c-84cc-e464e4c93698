# 🔍 模板传递诊断指南

## 🎯 问题分析

从您的日志 `userTemplatesCount: 0` 可以看出，WebSocket拦截器没有成功获取到用户配置的模板。

我已经添加了详细的调试日志和重试机制，现在请按照以下步骤进行诊断：

## 🚀 测试步骤

### 第一步：重新部署
1. **重新加载扩展**：
   - 打开 `chrome://extensions/`
   - 找到携程IM助手扩展
   - 点击刷新按钮 🔄

2. **完全刷新IM页面**：
   - 打开携程IM页面
   - 按 `Ctrl+F5` 完全刷新

### 第二步：配置测试模板
1. **打开扩展popup**
2. **进入"消息模板"页面**
3. **创建一个简单的测试模板**：
   ```
   模板名称: 测试模板
   内容: 这是一个测试回复
   关键词: 测试,test
   状态: ✅ 启用
   ```
4. **保存模板**

### 第三步：查看调试日志
打开浏览器控制台（F12），查看以下关键日志：

#### 🔍 预期的正常日志序列：

1. **WebSocket拦截器启动**：
   ```
   [CtripIM WebSocket] 🔄 尝试请求模板 (第 1 次)
   [CtripIM WebSocket] 📤 请求用户模板...
   ```

2. **Content Script响应**：
   ```
   [CtripIM Content] 📨 收到拦截器消息: ctripRequestTemplates
   [CtripIM Content] 📋 收到模板请求，开始处理...
   [CtripIM Content] 📤 通过引擎获取模板: 1 个
   [CtripIM Content] 📋 模板详情: [{id: "xxx", name: "测试模板", enabled: true, ...}]
   ```

3. **WebSocket拦截器接收**：
   ```
   [CtripIM WebSocket] 📋 已更新用户模板: 1 个
   [CtripIM WebSocket] 📋 模板详情: [{id: "xxx", name: "测试模板", enabled: true, keywords: ["测试","test"]}]
   ```

#### ❌ 可能的错误日志：

1. **Content Script未响应**：
   ```
   [CtripIM WebSocket] ⚠️ 未收到模板响应，准备重试...
   [CtripIM WebSocket] 🔄 尝试请求模板 (第 2 次)
   ```

2. **模板为空**：
   ```
   [CtripIM Content] ⚠️ 自动回复引擎不可用，发送空模板列表
   [CtripIM WebSocket] ⚠️ 收到空的模板列表
   ```

3. **存储问题**：
   ```
   [CtripIM Content] ❌ 从存储获取模板失败: ...
   ```

### 第四步：手动诊断
如果看不到预期日志，请在控制台执行以下诊断命令：

#### 1. 检查模板存储：
```javascript
chrome.storage.local.get(['replyTemplates']).then(result => {
  console.log('存储中的模板:', result.replyTemplates);
});
```

#### 2. 手动触发模板请求：
```javascript
window.postMessage({
  type: 'ctripRequestTemplates',
  source: 'websocket-interceptor',
  timestamp: Date.now()
}, '*');
```

#### 3. 检查事件监听器：
```javascript
console.log('WebSocket拦截器是否安装:', !!window._ctripWebSocketInterceptorInstalled);
```

## 🔧 可能的问题和解决方案

### 问题1：Content Script未加载
**症状**：看不到 `[CtripIM Content]` 相关日志
**解决**：
- 确保在正确的携程IM页面
- 检查扩展是否正确注入到页面
- 重新加载扩展

### 问题2：自动回复引擎未初始化
**症状**：看到 `自动回复引擎不可用` 日志
**解决**：
- 等待更长时间让引擎初始化
- 检查popup是否正确保存了模板

### 问题3：存储权限问题
**症状**：看到存储相关错误
**解决**：
- 检查扩展权限配置
- 重新安装扩展

### 问题4：跨世界通信失败
**症状**：拦截器发送请求但content script没有响应
**解决**：
- 检查页面是否有其他扩展干扰
- 尝试在隐身模式下测试

## 🎯 测试模板匹配

一旦看到正常的模板传递日志，发送测试消息：

1. **发送 "测试"**：
   - 预期日志：`[CtripIM WebSocket] 🎯 关键词匹配成功: {keyword: "测试", templateName: "测试模板"}`
   - 预期回复：`这是一个测试回复`

2. **发送 "随便什么"**：
   - 预期日志：`[CtripIM WebSocket] 📝 使用兜底回复 - 没有匹配的用户模板`
   - 预期回复：`您好！感谢您的消息，我们会尽快回复您。`

## 📊 成功标准

### ✅ 模板传递成功的标志：
1. 看到 `已更新用户模板: X 个` (X > 0)
2. 看到详细的模板信息输出
3. 关键词匹配时使用模板内容回复
4. 无匹配时使用兜底回复

### ❌ 需要进一步调试的情况：
1. 始终显示 `userTemplatesCount: 0`
2. 看不到content script的响应日志
3. 模板请求重试多次仍失败

## 🔄 下一步

请按照上述步骤测试，并将控制台的完整日志发送给我，我会根据具体的日志输出来进一步诊断和修复问题。

特别关注：
- 是否看到模板请求和响应的日志
- 模板数量是否正确
- 是否有任何错误信息

这样我们就能准确定位问题所在并进行针对性修复！🎯
